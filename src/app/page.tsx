'use client';

import { useState, useEffect } from 'react';
import { Position } from '@/types/position';
import { loadPositions, getAllTags } from '@/utils/storage';
import RouletteWheel from '@/components/RouletteWheel';
import PositionForm from '@/components/PositionForm';
import TagFilter from '@/components/TagFilter';
import PositionResult from '@/components/PositionResult';

export default function Home() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [isSpinning, setIsSpinning] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null);

  // Load positions on component mount
  useEffect(() => {
    const loadedPositions = loadPositions();
    setPositions(loadedPositions);
    setFilteredPositions(loadedPositions);
    setAvailableTags(getAllTags());
  }, []);

  // Filter positions when tags change
  useEffect(() => {
    if (selectedTags.length === 0) {
      setFilteredPositions(positions);
    } else {
      const filtered = positions.filter(position =>
        selectedTags.some(tag => position.tags.includes(tag))
      );
      setFilteredPositions(filtered);
    }
  }, [positions, selectedTags]);

  const handlePositionAdded = (newPosition: Position) => {
    const updatedPositions = [...positions, newPosition];
    setPositions(updatedPositions);
    setAvailableTags(getAllTags());
  };

  const handlePositionSelected = (position: Position) => {
    setSelectedPosition(position);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            🎯 Sexy Roulette
          </h1>
          <p className="text-gray-600">
            Spin the wheel for a random position suggestion!
          </p>
        </div>

        {/* Add Position Button */}
        <div className="text-center mb-8">
          <button
            onClick={() => setShowForm(true)}
            className="px-6 py-3 bg-pink-600 text-white font-semibold rounded-lg shadow-md hover:bg-pink-700 transition-colors"
          >
            + Add New Position
          </button>
        </div>

        {/* Tag Filter */}
        <TagFilter
          availableTags={availableTags}
          selectedTags={selectedTags}
          onTagsChange={setSelectedTags}
        />

        {/* Filter Info */}
        {selectedTags.length > 0 && (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-600">
              Showing {filteredPositions.length} of {positions.length} positions
            </p>
          </div>
        )}

        {/* Roulette Wheel */}
        <div className="flex justify-center mb-8">
          <RouletteWheel
            positions={filteredPositions}
            onPositionSelected={handlePositionSelected}
            isSpinning={isSpinning}
            setIsSpinning={setIsSpinning}
          />
        </div>

        {/* Positions Gallery */}
        {positions.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
              Your Positions ({positions.length})
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {positions.map(position => (
                <div
                  key={position.id}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <img
                    src={position.image}
                    alt={position.name}
                    className="w-full h-32 object-cover"
                  />
                  <div className="p-3">
                    <h3 className="font-semibold text-gray-800 mb-2">
                      {position.name}
                    </h3>
                    {position.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {position.tags.map(tag => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-pink-100 text-pink-800 rounded-full text-xs"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {positions.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg mb-4">
              No positions added yet!
            </p>
            <p className="text-gray-400">
              Click "Add New Position" to get started.
            </p>
          </div>
        )}
      </div>

      {/* Modals */}
      {showForm && (
        <PositionForm
          onPositionAdded={handlePositionAdded}
          onClose={() => setShowForm(false)}
        />
      )}

      {selectedPosition && (
        <PositionResult
          position={selectedPosition}
          onClose={() => setSelectedPosition(null)}
        />
      )}
    </div>
  );
}
