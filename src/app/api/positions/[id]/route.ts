import { NextRequest, NextResponse } from 'next/server';
import { deletePositionFromFile } from '@/lib/fileStorage';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Position ID is required' },
        { status: 400 }
      );
    }
    
    deletePositionFromFile(id);
    
    return NextResponse.json(
      { message: 'Position deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting position:', error);
    return NextResponse.json(
      { error: 'Failed to delete position' },
      { status: 500 }
    );
  }
}
