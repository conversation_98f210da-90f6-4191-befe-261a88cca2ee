import { NextRequest, NextResponse } from 'next/server';
import { loadPositionsFromFile, addPositionToFile, getAllTagsFromFile } from '@/lib/fileStorage';

export async function GET() {
  try {
    const positions = loadPositionsFromFile();
    const tags = getAllTagsFromFile();
    
    return NextResponse.json({
      positions,
      tags
    });
  } catch (error) {
    console.error('Error fetching positions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch positions' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, tags, image } = body;
    
    if (!name || !image) {
      return NextResponse.json(
        { error: 'Name and image are required' },
        { status: 400 }
      );
    }
    
    const newPosition = addPositionToFile(
      {
        name: name.trim(),
        tags: tags || []
      },
      image
    );
    
    return NextResponse.json(newPosition, { status: 201 });
  } catch (error) {
    console.error('Error creating position:', error);
    return NextResponse.json(
      { error: 'Failed to create position' },
      { status: 500 }
    );
  }
}
