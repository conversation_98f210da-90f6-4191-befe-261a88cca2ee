import { Position } from '@/types/position';

const STORAGE_KEY = 'sexy-roulette-positions';

export const savePositions = (positions: Position[]): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(positions));
  }
};

export const loadPositions = (): Position[] => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        return parsed.map((p: any) => ({
          ...p,
          createdAt: new Date(p.createdAt)
        }));
      } catch (error) {
        console.error('Error parsing stored positions:', error);
        return [];
      }
    }
  }
  return [];
};

export const addPosition = (position: Omit<Position, 'id' | 'createdAt'>): Position => {
  const newPosition: Position = {
    ...position,
    id: crypto.randomUUID(),
    createdAt: new Date()
  };
  
  const positions = loadPositions();
  positions.push(newPosition);
  savePositions(positions);
  
  return newPosition;
};

export const deletePosition = (id: string): void => {
  const positions = loadPositions();
  const filtered = positions.filter(p => p.id !== id);
  savePositions(filtered);
};

export const getAllTags = (): string[] => {
  const positions = loadPositions();
  const allTags = positions.flatMap(p => p.tags);
  return Array.from(new Set(allTags)).sort();
};
