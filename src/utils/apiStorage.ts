import { Position } from '@/types/position';

// API endpoints
const API_BASE = '/api';

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

// Fetch positions and tags from API
export const loadPositions = async (): Promise<{ positions: Position[]; tags: string[] }> => {
  try {
    const response = await fetch(`${API_BASE}/positions`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Convert date strings back to Date objects
    const positions = data.positions.map((p: any) => ({
      ...p,
      createdAt: new Date(p.createdAt)
    }));
    
    return {
      positions,
      tags: data.tags || []
    };
  } catch (error) {
    console.error('Error loading positions:', error);
    return { positions: [], tags: [] };
  }
};

// Add a new position
export const addPosition = async (position: Omit<Position, 'id' | 'createdAt'>): Promise<Position | null> => {
  try {
    const response = await fetch(`${API_BASE}/positions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(position),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    
    const newPosition = await response.json();
    
    // Convert date string back to Date object
    return {
      ...newPosition,
      createdAt: new Date(newPosition.createdAt)
    };
  } catch (error) {
    console.error('Error adding position:', error);
    throw error;
  }
};

// Delete a position
export const deletePosition = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE}/positions/${id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting position:', error);
    throw error;
  }
};

// Helper function to get all tags (extracted from positions)
export const getAllTags = (positions: Position[]): string[] => {
  const allTags = positions.flatMap(p => p.tags);
  return Array.from(new Set(allTags)).sort();
};

// Error handling wrapper for API calls
export const withErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  errorMessage: string = 'An error occurred'
): Promise<ApiResponse<T>> => {
  try {
    const data = await apiCall();
    return { data };
  } catch (error) {
    console.error(errorMessage, error);
    return { 
      error: error instanceof Error ? error.message : errorMessage 
    };
  }
};
