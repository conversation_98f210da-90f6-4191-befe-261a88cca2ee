import fs from 'fs';
import path from 'path';
import { Position } from '@/types/position';

// Storage paths
const DATA_DIR = path.join(process.cwd(), 'data');
const IMAGES_DIR = path.join(DATA_DIR, 'images');
const DB_FILE = path.join(DATA_DIR, 'positions.json');

// Ensure directories exist
export const ensureDirectories = () => {
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  }
  if (!fs.existsSync(IMAGES_DIR)) {
    fs.mkdirSync(IMAGES_DIR, { recursive: true });
  }
};

// Database operations
export const loadPositionsFromFile = (): Position[] => {
  ensureDirectories();
  
  if (!fs.existsSync(DB_FILE)) {
    return [];
  }
  
  try {
    const data = fs.readFileSync(DB_FILE, 'utf8');
    const positions = JSON.parse(data);
    return positions.map((p: any) => ({
      ...p,
      createdAt: new Date(p.createdAt)
    }));
  } catch (error) {
    console.error('Error loading positions:', error);
    return [];
  }
};

export const savePositionsToFile = (positions: Position[]): void => {
  ensureDirectories();
  
  try {
    fs.writeFileSync(DB_FILE, JSON.stringify(positions, null, 2));
  } catch (error) {
    console.error('Error saving positions:', error);
    throw new Error('Failed to save positions');
  }
};

// Image operations
export const saveImageFile = (imageData: string, filename: string): string => {
  ensureDirectories();
  
  try {
    // Remove data URL prefix if present
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    
    const imagePath = path.join(IMAGES_DIR, filename);
    fs.writeFileSync(imagePath, buffer);
    
    // Return the public URL path
    return `/api/images/${filename}`;
  } catch (error) {
    console.error('Error saving image:', error);
    throw new Error('Failed to save image');
  }
};

export const deleteImageFile = (filename: string): void => {
  try {
    const imagePath = path.join(IMAGES_DIR, filename);
    if (fs.existsSync(imagePath)) {
      fs.unlinkSync(imagePath);
    }
  } catch (error) {
    console.error('Error deleting image:', error);
  }
};

export const getImageFile = (filename: string): Buffer | null => {
  try {
    const imagePath = path.join(IMAGES_DIR, filename);
    if (fs.existsSync(imagePath)) {
      return fs.readFileSync(imagePath);
    }
    return null;
  } catch (error) {
    console.error('Error reading image:', error);
    return null;
  }
};

// Position CRUD operations
export const addPositionToFile = (positionData: Omit<Position, 'id' | 'createdAt' | 'image'>, imageData: string): Position => {
  const id = crypto.randomUUID();
  const timestamp = new Date();
  
  // Generate unique filename
  const imageExtension = getImageExtension(imageData);
  const imageFilename = `${id}.${imageExtension}`;
  
  // Save image file
  const imageUrl = saveImageFile(imageData, imageFilename);
  
  // Create position object
  const newPosition: Position = {
    id,
    name: positionData.name,
    tags: positionData.tags,
    image: imageUrl,
    createdAt: timestamp
  };
  
  // Add to database
  const positions = loadPositionsFromFile();
  positions.push(newPosition);
  savePositionsToFile(positions);
  
  return newPosition;
};

export const deletePositionFromFile = (id: string): void => {
  const positions = loadPositionsFromFile();
  const position = positions.find(p => p.id === id);
  
  if (position) {
    // Delete image file
    const filename = path.basename(position.image);
    deleteImageFile(filename);
    
    // Remove from database
    const updatedPositions = positions.filter(p => p.id !== id);
    savePositionsToFile(updatedPositions);
  }
};

export const getAllTagsFromFile = (): string[] => {
  const positions = loadPositionsFromFile();
  const allTags = positions.flatMap(p => p.tags);
  return Array.from(new Set(allTags)).sort();
};

// Helper functions
const getImageExtension = (imageData: string): string => {
  const match = imageData.match(/^data:image\/([a-z]+);base64,/);
  return match ? match[1] : 'jpg';
};
