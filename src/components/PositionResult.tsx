'use client';

import { Position } from '@/types/position';

interface PositionResultProps {
  position: Position | null;
  onClose: () => void;
}

export default function PositionResult({ position, onClose }: PositionResultProps) {
  if (!position) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50 animate-slide-up">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto animate-bounce-in shadow-2xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-pink-600">🎉 Your Selection!</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="text-center">
          <div className="mb-4">
            <img
              src={position.image}
              alt={position.name}
              className="w-full h-48 object-cover rounded-lg shadow-md"
            />
          </div>

          <h3 className="text-xl font-semibold text-gray-800 mb-3">
            {position.name}
          </h3>

          {position.tags.length > 0 && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Tags:</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {position.tags.map(tag => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-pink-100 text-pink-800 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              Got it! 😉
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
