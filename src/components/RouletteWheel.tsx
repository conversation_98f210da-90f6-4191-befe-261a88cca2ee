'use client';

import { useState, useRef } from 'react';
import { Position } from '@/types/position';

interface RouletteWheelProps {
  positions: Position[];
  onPositionSelected: (position: Position) => void;
  isSpinning: boolean;
  setIsSpinning: (spinning: boolean) => void;
}

export default function RouletteWheel({ 
  positions, 
  onPositionSelected, 
  isSpinning, 
  setIsSpinning 
}: RouletteWheelProps) {
  const [rotation, setRotation] = useState(0);
  const wheelRef = useRef<HTMLDivElement>(null);

  const colors = [
    '#FF6B9D', '#FF8E9B', '#FFB4B4', '#FFDAB9', '#E6E6FA',
    '#DDA0DD', '#F0E68C', '#98FB98', '#87CEEB', '#F4A460',
    '#FFA07A', '#20B2AA', '#87CEFA', '#DEB887', '#F5DEB3'
  ];

  const spinWheel = () => {
    if (positions.length === 0 || isSpinning) return;

    setIsSpinning(true);
    
    // Calculate random rotation (multiple full rotations + random angle)
    const minSpins = 3;
    const maxSpins = 6;
    const spins = Math.random() * (maxSpins - minSpins) + minSpins;
    const finalAngle = Math.random() * 360;
    const totalRotation = rotation + (spins * 360) + finalAngle;
    
    setRotation(totalRotation);

    // Calculate which position was selected
    setTimeout(() => {
      const normalizedAngle = (360 - (totalRotation % 360)) % 360;
      const segmentAngle = 360 / positions.length;
      const selectedIndex = Math.floor(normalizedAngle / segmentAngle);
      const selectedPosition = positions[selectedIndex];
      
      onPositionSelected(selectedPosition);
      setIsSpinning(false);
    }, 3000); // Match the CSS transition duration
  };

  if (positions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="w-64 h-64 border-4 border-dashed border-gray-300 rounded-full flex items-center justify-center">
          <p className="text-gray-500 text-center">
            Add some positions to start spinning!
          </p>
        </div>
      </div>
    );
  }

  const segmentAngle = 360 / positions.length;

  return (
    <div className="flex flex-col items-center">
      <div className="relative">
        {/* Pointer */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-10">
          <div className={`w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-red-600 ${isSpinning ? 'animate-pulse' : ''}`}></div>
        </div>

        {/* Wheel */}
        <div
          ref={wheelRef}
          className={`w-64 h-64 rounded-full relative overflow-hidden border-4 border-gray-800 transition-transform duration-3000 ease-out ${isSpinning ? 'animate-pulse-glow' : 'shadow-lg'}`}
          style={{
            transform: `rotate(${rotation}deg)`,
            transitionTimingFunction: 'cubic-bezier(0.25, 0.1, 0.25, 1)'
          }}
        >
          {positions.map((position, index) => {
            const startAngle = index * segmentAngle;
            const endAngle = (index + 1) * segmentAngle;
            const midAngle = (startAngle + endAngle) / 2;
            
            // Create SVG path for the segment
            const radius = 128; // Half of wheel size
            const centerX = 128;
            const centerY = 128;
            
            const startX = centerX + radius * Math.cos((startAngle - 90) * Math.PI / 180);
            const startY = centerY + radius * Math.sin((startAngle - 90) * Math.PI / 180);
            const endX = centerX + radius * Math.cos((endAngle - 90) * Math.PI / 180);
            const endY = centerY + radius * Math.sin((endAngle - 90) * Math.PI / 180);
            
            const largeArcFlag = segmentAngle > 180 ? 1 : 0;
            
            const pathData = [
              `M ${centerX} ${centerY}`,
              `L ${startX} ${startY}`,
              `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY}`,
              'Z'
            ].join(' ');

            // Text position - start closer to center and extend outward
            const textStartRadius = radius * 0.25;
            const textStartX = centerX + textStartRadius * Math.cos((midAngle - 90) * Math.PI / 180);
            const textStartY = centerY + textStartRadius * Math.sin((midAngle - 90) * Math.PI / 180);

            // Adjust text rotation so it reads from center outward
            // For angles in the bottom half, flip the text to avoid upside-down reading
            const textAngle = midAngle > 90 && midAngle < 270 ? midAngle + 180 : midAngle;
            const textAnchor = midAngle > 90 && midAngle < 270 ? "end" : "start";

            return (
              <svg
                key={position.id}
                className="absolute inset-0 w-full h-full"
                viewBox="0 0 256 256"
              >
                <path
                  d={pathData}
                  fill={colors[index % colors.length]}
                  stroke="#fff"
                  strokeWidth="1"
                />
                <text
                  x={textStartX}
                  y={textStartY}
                  textAnchor={textAnchor}
                  dominantBaseline="middle"
                  className="fill-gray-800 text-xs font-bold"
                  transform={`rotate(${textAngle}, ${textStartX}, ${textStartY})`}
                >
                  {position.name.length > 18
                    ? position.name.substring(0, 18) + '...'
                    : position.name}
                </text>
              </svg>
            );
          })}
        </div>
      </div>
      
      {/* Spin Button */}
      <button
        onClick={spinWheel}
        disabled={isSpinning}
        className={`mt-6 px-8 py-3 bg-gradient-to-r from-pink-500 to-red-500 text-white font-bold rounded-full shadow-lg hover:from-pink-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 ${isSpinning ? 'animate-pulse' : ''}`}
      >
        {isSpinning ? '🎯 Spinning...' : '🎯 SPIN THE WHEEL!'}
      </button>
    </div>
  );
}
