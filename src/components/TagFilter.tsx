'use client';

import { useState } from 'react';

interface TagFilterProps {
  availableTags: string[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
}

export default function TagFilter({ availableTags, selectedTags, onTagsChange }: TagFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagsChange(selectedTags.filter(t => t !== tag));
    } else {
      onTagsChange([...selectedTags, tag]);
    }
  };

  const clearAllTags = () => {
    onTagsChange([]);
  };

  if (availableTags.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-800">Filter by Tags</h3>
        <div className="flex items-center gap-2">
          {selectedTags.length > 0 && (
            <button
              onClick={clearAllTags}
              className="text-sm text-red-600 hover:text-red-800 underline"
            >
              Clear All
            </button>
          )}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-600 hover:text-gray-800"
          >
            {isExpanded ? '▲' : '▼'}
          </button>
        </div>
      </div>

      {selectedTags.length > 0 && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 mb-2">
            Active filters ({selectedTags.length}):
          </p>
          <div className="flex flex-wrap gap-2">
            {selectedTags.map(tag => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-pink-100 text-pink-800 border border-pink-200"
              >
                {tag}
                <button
                  onClick={() => toggleTag(tag)}
                  className="ml-2 text-pink-600 hover:text-pink-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {isExpanded && (
        <div className="space-y-2">
          <p className="text-sm text-gray-600">Available tags:</p>
          <div className="flex flex-wrap gap-2">
            {availableTags.map(tag => {
              const isSelected = selectedTags.includes(tag);
              return (
                <button
                  key={tag}
                  onClick={() => toggleTag(tag)}
                  className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                    isSelected
                      ? 'bg-pink-500 text-white border-pink-500'
                      : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {tag}
                </button>
              );
            })}
          </div>
        </div>
      )}

      {!isExpanded && availableTags.length > 0 && (
        <p className="text-sm text-gray-500">
          {availableTags.length} tag{availableTags.length !== 1 ? 's' : ''} available
        </p>
      )}
    </div>
  );
}
