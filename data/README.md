# Local Data Storage

This directory contains the local file storage for the Sexy Roulette application.

## Structure

- `positions.json` - JSON database containing position metadata
- `images/` - Directory containing uploaded image files

## Files

### positions.json
Contains an array of position objects with the following structure:
```json
[
  {
    "id": "uuid-string",
    "name": "Position Name",
    "tags": ["tag1", "tag2"],
    "image": "/api/images/uuid.jpg",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
]
```

### images/
Image files are stored with UUID-based filenames:
- Format: `{uuid}.{extension}`
- Supported formats: jpg, png, gif, webp
- Served via `/api/images/{filename}` endpoint

## Backup

To backup your data:
1. Copy the entire `data/` directory
2. Both `positions.json` and `images/` folder are needed for complete backup

## Restore

To restore from backup:
1. Replace the `data/` directory with your backup
2. Restart the application
